package com.pcitc.geodrawing.domain.entity.recognition;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DetectedResult extends BoundingBox {
    private String id;
    private Integer classId;
    private String className;
    private Double confidence;
    private Integer imageIndex;
    private String originalText;
    private Difficult difficult;

    public void setBoundingBox(Bbox bbox) {
        this.x1 = bbox.getX().intValue();
        this.y1 = bbox.getY().intValue();
        this.x2 = (int) (bbox.getX() + bbox.getWidth());
        this.y2 = (int) (bbox.getY() + bbox.getHeight());
    }

    public Bbox getBoundingBox() {
        int x1 = this.x1 == null ? 0 : this.x1;
        int y1 = this.y1 == null ? 0 : this.y1;
        int x2 = this.x2 == null ? 0 : this.x2;
        int y2 = this.y2 == null ? 0 : this.y2;
        int x = Math.min(x1, x2);
        int y = Math.min(y1, y2);
        int width = Math.abs(x2 - x1);
        int height = Math.abs(y2 - y1);
        return new Bbox((double) x, (double) y, (double) width, (double) height);
    }
}
