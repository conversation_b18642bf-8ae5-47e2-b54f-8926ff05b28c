package com.pcitc.geodrawing.application.strategy.report.impl;

import com.pcitc.geodrawing.application.helper.SectionViewHelper;
import com.pcitc.geodrawing.application.strategy.report.ReportConfigFactory;
import com.pcitc.geodrawing.common.constants.ReportConstants;
import com.pcitc.geodrawing.domain.entity.recognition.DepthElevationPair;
import com.pcitc.geodrawing.domain.entity.recognition.DrillHole;
import com.pcitc.geodrawing.domain.entity.report.CellStyle;
import com.pcitc.geodrawing.domain.entity.report.MergeCell;
import com.pcitc.geodrawing.domain.entity.report.SheetConfig;
import com.pcitc.geodrawing.infrastructure.persistence.mongo.entity.SectionViewResultEntity;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component(ReportConstants.LAYER_INFO)
public class LayerInfoConfigStrategy extends BaseConfigStrategyAdapter<List<SectionViewResultEntity>> {

    /**
     * 构建分层层地信息汇总表配置
     *
     * @param sectionViewResultEntities 剖面视图结果实体列表
     * @return SheetConfigDto 包含层位信息的汇总表配置
     */
    @Override
    public SheetConfig buildSummarySheetConfig(List<SectionViewResultEntity> sectionViewResultEntities) {
        List<List<Object>> data = new ArrayList<>();

        // 获取所有钻孔
        List<DrillHole> drillHoles = SectionViewHelper.getAllDrillHoles(sectionViewResultEntities);

        CellStyle redCell = redCellStyle();

        // 获取最大层数（用于生成表头）
        int maxLayerCount = drillHoles.stream()
                .mapToInt(dh -> dh.getDepthElevationPairs() == null ? 0 : dh.getDepthElevationPairs().size())
                .max()
                .orElse(0);

        // ======== 生成表头 ========

        // 第一行：A1:A3 为“孔号”，B1 到 倒数第二列 为“分层层底信息”
        int totalCols = 1 + maxLayerCount * 2 + 1; // 孔号 + 每层2列 + 备注
        List<Object> row1 = new ArrayList<>();
        row1.add("孔号");
        // 分层层底信息占用 maxLayerCount*2 列
        row1.add("分层层底信息占用");
        row1.add("备注");
        data.add(row1);

        // 第二行：B2:C2 显示第1层，D2:E2 显示第2层...
        List<Object> row2 = new ArrayList<>();
        row2.add("");
        for (int layerIdx = 1; layerIdx <= maxLayerCount; layerIdx++) {
            row2.add("第" + layerIdx + "层");
            row2.add("");
        }
        row2.add("");
        data.add(row2);

        // 第三行：每层的两个子列（深度、标高）
        List<Object> row3 = new ArrayList<>();
        row3.add("");
        for (int i = 0; i < maxLayerCount; i++) {
            row3.add("深度");
            row3.add("标高");
        }
        row3.add("");
        data.add(row3);

        // ======== 数据行 ========
        Map<String, CellStyle> difficultCellStyles = new HashMap<>(); // 记录要标红的单元格

        for (int rowIdx = 0; rowIdx < drillHoles.size(); rowIdx++) {
            DrillHole drillHole = drillHoles.get(rowIdx);
            List<Object> row = new ArrayList<>();
            row.add(drillHole.getDrillName());

            // 输出每层深度/标高
            List<DepthElevationPair> pairs = drillHole.getDepthElevationPairs();
            for (int i = 0; i < maxLayerCount; i++) {
                if (pairs != null && i < pairs.size()) {
                    DepthElevationPair p = pairs.get(i);
                    int excelRow = 4 + rowIdx; // 数据从第4行开始
                    int depthColIndex = 2 + i * 2; // 深度列序号（从1开始计）
                    int elevColIndex = 3 + i * 2;  // 标高列序号
                    if(p.getDifficult() != null){
                        difficultCellStyles.put(getColumnLetter(depthColIndex) + excelRow,
                                redCell);
                        difficultCellStyles.put(getColumnLetter(elevColIndex) + excelRow,
                                redCell);
                    }
                    row.add(p.getDepth() != null ? p.getDepth() : "");
                    row.add(p.getElevation() != null ? p.getElevation() : "");
                } else {
                    row.add("");
                    row.add("");
                }
            }
            // 备注列
            row.add(drillHole.getSectionName());
            data.add(row);
        }

        // ======== 合并单元格 ========
        List<MergeCell> mergeCells = MergeCellBuilder.builder()
                .addMergeCell("A1:A3", "孔号")
                .addMergeCell("B1:" + getColumnLetter(1 + maxLayerCount * 2) + "1", "分层层底信息")
                .addMergeCell(getColumnLetter(totalCols) + "1:" + getColumnLetter(totalCols) + "3", "备注")
                .build();

        // 层标题合并（B2:C2, D2:E2, ...）
        for (int layerIdx = 0; layerIdx < maxLayerCount; layerIdx++) {
            String startCol = getColumnLetter(2 + layerIdx * 2);
            String endCol = getColumnLetter(3 + layerIdx * 2);
            mergeCells.add(new MergeCell(startCol + "2"+ endCol + "2", "第" + (layerIdx + 1) + "层", null));
        }

        // 样式
        Map<String, CellStyle> styles = StyleBuilder.builder()
                .addHeaderStyle("A1:" + getColumnLetter(totalCols) + "3")
                .addDataStyle("A4:" + getColumnLetter(totalCols) + (drillHoles.size() + 3))
                .addCustomStyle(difficultCellStyles)
                .build();

        return SheetConfig.builder()
                .name("层数据报表")
                .data(data)
                .mergeCells(mergeCells)
                .styles(styles)
                .startRow(1)
                .startCol(1)
                .build();
    }

    private CellStyle redCellStyle() {
        return new CellStyle(
                "微软雅黑",
                11,
                false,
                false,
                "#000000",
                "#FF0000", // 背景红色
                true,
                "#000000",
                "center",
                "center",
                "General"
        );
    }

}
